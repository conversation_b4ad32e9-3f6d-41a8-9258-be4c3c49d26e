"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Loader2,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Eye,
  Download,
  Search,
  Filter,
  Trash2,
  Activity,
  Pause
} from 'lucide-react';
import { ExecutionPreviewModal } from '@/components/workflow/execution-preview-modal';
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbI<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>Link,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>crumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { format } from "date-fns";

interface ExecutionLog {
  id: string;
  workflowId: string;
  workflowName: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  userId: string;
  userName?: string;
  results?: any;
  logs?: any[];
  error?: string;
}

export default function WorkflowLogsPage() {
  const [logs, setLogs] = useState<ExecutionLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<ExecutionLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedLog, setSelectedLog] = useState<ExecutionLog | null>(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadLogs();
  }, []);

  useEffect(() => {
    filterLogs();
  }, [logs, searchTerm, statusFilter]);

  const loadLogs = async () => {
    setLoading(true);
    try {
      // Load from multiple sources
      const response = await fetch('/api/workflow-logs');
      
      if (response.ok) {
        const data = await response.json();
        setLogs(data.logs || []);
      } else {
        console.error('Failed to load logs');
        setLogs([]);
      }
    } catch (error) {
      console.error('Error loading logs:', error);
      setLogs([]);
    } finally {
      setLoading(false);
    }
  };

  const filterLogs = () => {
    let filtered = logs;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(log => 
        log.workflowName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.userName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(log => log.status === statusFilter);
    }

    setFilteredLogs(filtered);
  };

  // Helper functions for status display (matching workflow manager style)
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: { variant: "default" as const, icon: <Activity className="h-3 w-3" />, color: "bg-blue-500" },
      completed: { variant: "default" as const, icon: <CheckCircle className="h-3 w-3" />, color: "bg-green-500" },
      failed: { variant: "destructive" as const, icon: <XCircle className="h-3 w-3" />, color: "bg-red-500" },
      cancelled: { variant: "secondary" as const, icon: <XCircle className="h-3 w-3" />, color: "bg-gray-500" },
      paused: { variant: "outline" as const, icon: <Pause className="h-3 w-3" />, color: "bg-yellow-500" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.cancelled;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        {config.icon}
        {status}
      </Badge>
    );
  };

  const formatDuration = (duration: number | null) => {
    if (!duration) return 'N/A';
    
    const seconds = Math.floor(duration / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ${seconds % 60}s`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m`;
  };

  const handlePreviewLog = (log: ExecutionLog) => {
    // Convert log to execution status format
    const executionStatus = {
      workflowId: log.workflowId,
      executionId: log.id,
      status: log.status,
      startTime: new Date(log.startTime),
      endTime: log.endTime ? new Date(log.endTime) : undefined,
      progress: log.status === 'completed' ? 100 : 0,
      results: log.results || {},
      logs: log.logs || [],
      completedNodes: [],
      failedNodes: [],
      error: log.error
    };

    setSelectedLog(log);
    setShowPreviewModal(true);
  };

  const exportLog = (log: ExecutionLog) => {
    const exportData = {
      executionId: log.id,
      workflowId: log.workflowId,
      workflowName: log.workflowName,
      status: log.status,
      startTime: log.startTime,
      endTime: log.endTime,
      duration: log.duration,
      results: log.results,
      logs: log.logs,
      error: log.error,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-log-${log.id}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const deleteLog = async (executionId: string) => {
    if (confirm('Are you sure you want to delete this execution log? This action cannot be undone.')) {
      try {
        const response = await fetch('/api/workflow-logs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'delete', executionId })
        });

        if (response.ok) {
          // Remove from local state
          setLogs(prev => prev.filter(log => log.id !== executionId));
          setFilteredLogs(prev => prev.filter(log => log.id !== executionId));
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to delete log');
        }
      } catch (error) {
        console.error('Error deleting log:', error);
        setError('Failed to delete log');
      }
    }
  };

  const clearLogs = async () => {
    if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
      try {
        const response = await fetch('/api/workflow-logs', {
          method: 'DELETE'
        });

        if (response.ok) {
          setLogs([]);
          setFilteredLogs([]);
        }
      } catch (error) {
        console.error('Error clearing logs:', error);
      }
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbPage>Logs</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="mb-4">
            <h1 className="text-3xl font-bold">Workflow Logs</h1>
            <p className="text-muted-foreground">
              View and manage execution logs from all workflows with detailed results and export options
            </p>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Execution Logs</CardTitle>
                  <CardDescription>
                    View and manage execution logs from all workflows with detailed results and export options
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={loadLogs}
                    disabled={loading}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={clearLogs}
                    disabled={loading || logs.length === 0}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear Logs
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Filters */}
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search workflows, users, or execution IDs..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="running">Running</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {error && (
                <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded flex items-center gap-2">
                  <XCircle className="h-4 w-4" />
                  <span>{error}</span>
                </div>
              )}

              {loading ? (
                <div className="flex justify-center items-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : filteredLogs.length === 0 ? (
                <div className="text-center py-10 text-muted-foreground">
                  <p>{logs.length === 0 ? 'No execution logs found.' : 'No logs match the current filters.'}</p>
                  <p className="mt-2">
                    Execute some workflows to see their logs here.
                  </p>
                </div>
              ) : (
                <ScrollArea className="w-full h-[400px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Workflow</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Started</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLogs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell className="font-medium">
                            <div className="space-y-1">
                              <div className="truncate max-w-[200px]" title={log.workflowName}>
                                {log.workflowName}
                              </div>
                              <div className="text-xs text-muted-foreground truncate max-w-[200px]" title={log.id}>
                                {log.id.substring(0, 8)}...
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(log.status)}
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm">
                                {format(new Date(log.startTime), "MMM d, yyyy")}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {format(new Date(log.startTime), "HH:mm:ss")}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {formatDuration(log.duration)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm truncate max-w-[120px]" title={log.userName}>
                              {log.userName || 'Unknown'}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handlePreviewLog(log)}
                                title="View Results"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => exportLog(log)}
                                title="Export Results"
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => deleteLog(log.id)}
                                title="Delete Log"
                                className="text-destructive hover:text-destructive"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </div>
      </SidebarInset>

      {/* Preview Modal */}
      {selectedLog && (
        <ExecutionPreviewModal
          open={showPreviewModal}
          onOpenChange={setShowPreviewModal}
          executionStatus={{
            workflowId: selectedLog.workflowId,
            executionId: selectedLog.id,
            status: selectedLog.status,
            startTime: new Date(selectedLog.startTime),
            endTime: selectedLog.endTime ? new Date(selectedLog.endTime) : undefined,
            progress: selectedLog.status === 'completed' ? 100 : 0,
            results: selectedLog.results || {},
            logs: selectedLog.logs || [],
            completedNodes: [],
            failedNodes: [],
            error: selectedLog.error
          }}
          workflowId={selectedLog.workflowId}
        />
      )}
    </SidebarProvider>
  );
}
