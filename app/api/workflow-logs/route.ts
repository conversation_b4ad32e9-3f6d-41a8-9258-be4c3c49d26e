import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '50');
    const status = searchParams.get('status');
    const workflowId = searchParams.get('workflowId');

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id;

    console.log(`[Workflow Logs] Loading logs for user ${userId}`);

    // Build where clause
    const where: any = {};
    
    // Filter by user if not admin
    if (userId && !(session?.user as any)?.isAdmin) {
      where.userId = userId;
    }

    // Filter by status if provided
    if (status && status !== 'all') {
      where.status = status;
    }

    // Filter by workflow if provided
    if (workflowId) {
      where.workflowId = workflowId;
    }

    // Get total count
    const totalCount = await prisma.workflowExecution.count({ where });

    // Get executions with pagination
    const executions = await prisma.workflowExecution.findMany({
      where,
      include: {
        workflow: {
          select: {
            name: true
          }
        },
        user: {
          select: {
            name: true,
            username: true,
            email: true
          }
        }
      },
      orderBy: { startTime: 'desc' },
      take: pageSize,
      skip: (page - 1) * pageSize
    });

    // Format the response
    const logs = executions.map(execution => ({
      id: execution.id,
      workflowId: execution.workflowId,
      workflowName: execution.workflow?.name || `Workflow ${execution.workflowId}`,
      status: execution.status,
      startTime: execution.startTime.toISOString(),
      endTime: execution.endTime?.toISOString(),
      duration: execution.endTime && execution.startTime
        ? execution.endTime.getTime() - execution.startTime.getTime()
        : null,
      userId: execution.userId,
      userName: execution.user?.name || execution.user?.username || execution.user?.email || 'Unknown',
      results: execution.results ? JSON.parse(execution.results) : null,
      logs: execution.logs ? JSON.parse(execution.logs) : [],
      error: execution.error,
      variables: execution.variables ? JSON.parse(execution.variables) : null,
      options: execution.options ? JSON.parse(execution.options) : null,
      triggerNodeId: execution.triggerNodeId,
      progress: execution.progress || 0
    }));

    return NextResponse.json({
      logs,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    });

  } catch (error) {
    console.error('[Workflow Logs] Error loading logs:', error);
    return NextResponse.json(
      { 
        error: 'Failed to load workflow logs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id;
    const isAdmin = (session?.user as any)?.isAdmin;

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Workflow Logs] Clearing logs for user ${userId}`);

    // Build where clause - only allow users to clear their own logs unless admin
    const where: any = {};
    if (!isAdmin) {
      where.userId = userId;
    }

    // Delete executions
    const result = await prisma.workflowExecution.deleteMany({ where });

    console.log(`[Workflow Logs] Cleared ${result.count} execution logs`);

    return NextResponse.json({
      success: true,
      message: `Cleared ${result.count} execution logs`,
      deletedCount: result.count
    });

  } catch (error) {
    console.error('[Workflow Logs] Error clearing logs:', error);
    return NextResponse.json(
      { 
        error: 'Failed to clear workflow logs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, executionId, workflowId } = body;

    // Get session for user context
    const session = await getServerSession(authOptions);
    const userId = (session?.user as any)?.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    switch (action) {
      case 'retry':
        if (!executionId) {
          return NextResponse.json(
            { error: 'Execution ID required for retry' },
            { status: 400 }
          );
        }

        // Get the original execution
        const originalExecution = await prisma.workflowExecution.findUnique({
          where: { id: executionId },
          include: { workflow: true }
        });

        if (!originalExecution) {
          return NextResponse.json(
            { error: 'Execution not found' },
            { status: 404 }
          );
        }

        // Create a new execution based on the original
        const newExecution = await prisma.workflowExecution.create({
          data: {
            id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            workflowId: originalExecution.workflowId,
            userId: userId,
            status: 'queued',
            startTime: new Date(),
            variables: originalExecution.variables,
            options: originalExecution.options,
            triggerNodeId: originalExecution.triggerNodeId,
            logs: JSON.stringify([{
              timestamp: new Date(),
              level: 'info',
              message: `Retry of execution ${executionId}`
            }])
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Execution retry queued successfully',
          newExecutionId: newExecution.id
        });

      case 'export':
        if (!executionId) {
          return NextResponse.json(
            { error: 'Execution ID required for export' },
            { status: 400 }
          );
        }

        // Get execution data
        const execution = await prisma.workflowExecution.findUnique({
          where: { id: executionId },
          include: {
            workflow: { select: { name: true } },
            user: { select: { name: true, username: true, email: true } }
          }
        });

        if (!execution) {
          return NextResponse.json(
            { error: 'Execution not found' },
            { status: 404 }
          );
        }

        const exportData = {
          executionId: execution.id,
          workflowId: execution.workflowId,
          workflowName: execution.workflow?.name || `Workflow ${execution.workflowId}`,
          status: execution.status,
          startTime: execution.startTime.toISOString(),
          endTime: execution.endTime?.toISOString(),
          duration: execution.endTime && execution.startTime
            ? execution.endTime.getTime() - execution.startTime.getTime()
            : null,
          results: execution.results ? JSON.parse(execution.results) : null,
          logs: execution.logs ? JSON.parse(execution.logs) : [],
          error: execution.error,
          variables: execution.variables ? JSON.parse(execution.variables) : null,
          options: execution.options ? JSON.parse(execution.options) : null,
          exportedAt: new Date().toISOString(),
          exportedBy: {
            id: userId,
            name: (session?.user as any)?.name || 'Unknown'
          }
        };

        return NextResponse.json({
          success: true,
          data: exportData,
          filename: `execution-${executionId}-${Date.now()}.json`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('[Workflow Logs] Error processing request:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
