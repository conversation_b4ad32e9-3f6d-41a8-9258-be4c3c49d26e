"use client";

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Loader2, 
  Download, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  FileJson,
  FileSpreadsheet,
  Copy
} from 'lucide-react';
import { WorkflowExecutionStatus } from '@/lib/workflow/execution-engine';

interface ExecutionPreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  executionStatus: WorkflowExecutionStatus | null;
  workflowId: string;
}

interface ExecutionSummary {
  executionId: string;
  workflowId: string;
  workflowName: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration: number;
  totalNodes: number;
  successfulNodes: number;
  failedNodes: number;
  outputData: any[];
  logs: any[];
  error?: string;
}

interface TableData {
  columns: { key: string; label: string }[];
  rows: Record<string, any>[];
  totalRows: number;
  currentPage: number;
  pageSize: number;
}

export function ExecutionPreviewModal({
  open,
  onOpenChange,
  executionStatus,
  workflowId
}: ExecutionPreviewModalProps) {
  const [summary, setSummary] = useState<ExecutionSummary | null>(null);
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('json');
  const [exporting, setExporting] = useState(false);

  // Load execution data when modal opens
  useEffect(() => {
    if (open && executionStatus) {
      loadExecutionData();
    }
  }, [open, executionStatus]);

  const loadExecutionData = async () => {
    if (!executionStatus) return;

    setLoading(true);
    setError(null);

    try {
      // For live execution status, use the current data
      if (executionStatus.status === 'running' || !executionStatus.workflowId) {
        // Create summary from current execution status
        const liveSummary: ExecutionSummary = {
          executionId: executionStatus.workflowId,
          workflowId: workflowId,
          workflowName: `Workflow ${workflowId}`,
          status: executionStatus.status,
          startTime: executionStatus.startTime.toISOString(),
          endTime: executionStatus.endTime?.toISOString(),
          duration: executionStatus.endTime 
            ? executionStatus.endTime.getTime() - executionStatus.startTime.getTime()
            : Date.now() - executionStatus.startTime.getTime(),
          totalNodes: Object.keys(executionStatus.results).length,
          successfulNodes: executionStatus.completedNodes.length,
          failedNodes: executionStatus.failedNodes.length,
          outputData: extractOutputData(executionStatus.results),
          logs: executionStatus.logs || [],
          error: executionStatus.error
        };

        setSummary(liveSummary);
        setTableData(createTableData(liveSummary.outputData));
      } else {
        // For completed executions, try to fetch from API
        try {
          const response = await fetch(`/api/workflows/executions/${executionStatus.workflowId}/results?format=summary`);
          if (response.ok) {
            const apiSummary = await response.json();
            setSummary(apiSummary);
            
            // Load table data
            const tableResponse = await fetch(`/api/workflows/executions/${executionStatus.workflowId}/results?format=table&page=1&pageSize=100`);
            if (tableResponse.ok) {
              const apiTableData = await tableResponse.json();
              setTableData(apiTableData);
            }
          } else {
            // Fallback to live data
            throw new Error('API not available');
          }
        } catch (apiError) {
          // Fallback to current execution status
          const fallbackSummary: ExecutionSummary = {
            executionId: executionStatus.workflowId,
            workflowId: workflowId,
            workflowName: `Workflow ${workflowId}`,
            status: executionStatus.status,
            startTime: executionStatus.startTime.toISOString(),
            endTime: executionStatus.endTime?.toISOString(),
            duration: executionStatus.endTime 
              ? executionStatus.endTime.getTime() - executionStatus.startTime.getTime()
              : Date.now() - executionStatus.startTime.getTime(),
            totalNodes: Object.keys(executionStatus.results).length,
            successfulNodes: executionStatus.completedNodes.length,
            failedNodes: executionStatus.failedNodes.length,
            outputData: extractOutputData(executionStatus.results),
            logs: executionStatus.logs || [],
            error: executionStatus.error
          };

          setSummary(fallbackSummary);
          setTableData(createTableData(fallbackSummary.outputData));
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load execution data');
    } finally {
      setLoading(false);
    }
  };

  const extractOutputData = (results: Record<string, any>): any[] => {
    const outputData: any[] = [];
    
    Object.entries(results).forEach(([nodeId, result]) => {
      if (result.success && result.outputs) {
        Object.entries(result.outputs).forEach(([outputKey, outputValue]) => {
          if (Array.isArray(outputValue)) {
            outputData.push(...outputValue.map((item, index) => ({
              nodeId,
              outputKey,
              index,
              ...item
            })));
          } else if (typeof outputValue === 'object' && outputValue !== null) {
            outputData.push({
              nodeId,
              outputKey,
              ...outputValue
            });
          } else {
            outputData.push({
              nodeId,
              outputKey,
              value: outputValue
            });
          }
        });
      }
    });

    return outputData;
  };

  const createTableData = (outputData: any[]): TableData => {
    if (outputData.length === 0) {
      return {
        columns: [],
        rows: [],
        totalRows: 0,
        currentPage: 1,
        pageSize: 100
      };
    }

    // Get all unique keys from the data
    const allKeys = new Set<string>();
    outputData.forEach(item => {
      Object.keys(item).forEach(key => allKeys.add(key));
    });

    const columns = Array.from(allKeys).map(key => ({
      key,
      label: key.charAt(0).toUpperCase() + key.slice(1)
    }));

    return {
      columns,
      rows: outputData,
      totalRows: outputData.length,
      currentPage: 1,
      pageSize: 100
    };
  };

  const exportAsJSON = () => {
    if (!summary) return;

    setExporting(true);
    try {
      const exportData = {
        summary,
        tableData: tableData?.rows || [],
        exportedAt: new Date().toISOString()
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `workflow-execution-${summary.executionId}-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } finally {
      setExporting(false);
    }
  };

  const exportAsCSV = () => {
    if (!tableData || tableData.rows.length === 0) return;

    setExporting(true);
    try {
      const headers = tableData.columns.map(col => col.label).join(',');
      const rows = tableData.rows.map(row => 
        tableData.columns.map(col => {
          const value = row[col.key];
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || '';
        }).join(',')
      );

      const csvContent = [headers, ...rows].join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `workflow-execution-${summary?.executionId}-${Date.now()}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } finally {
      setExporting(false);
    }
  };

  const copyToClipboard = async (data: any) => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(data, null, 2));
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ${seconds % 60}s`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m`;
  };

  if (!summary && !loading) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {summary && getStatusIcon(summary.status)}
            Execution Results Preview
          </DialogTitle>
          <DialogDescription>
            {summary ? (
              <>
                Execution ID: {summary.executionId} • 
                Started: {new Date(summary.startTime).toLocaleString()}
                {summary.endTime && (
                  <> • Duration: {formatDuration(summary.duration)}</>
                )}
              </>
            ) : (
              'Loading execution data...'
            )}
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading execution data...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <XCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-red-600">{error}</p>
            <Button onClick={loadExecutionData} className="mt-2">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        ) : summary ? (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-3">
                  <div className="text-2xl font-bold text-green-600">
                    {summary.successfulNodes}
                  </div>
                  <div className="text-xs text-muted-foreground">Successful</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3">
                  <div className="text-2xl font-bold text-red-600">
                    {summary.failedNodes}
                  </div>
                  <div className="text-xs text-muted-foreground">Failed</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3">
                  <div className="text-2xl font-bold text-blue-600">
                    {summary.totalNodes}
                  </div>
                  <div className="text-xs text-muted-foreground">Total Nodes</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3">
                  <div className="text-2xl font-bold">
                    {summary.outputData.length}
                  </div>
                  <div className="text-xs text-muted-foreground">Output Records</div>
                </CardContent>
              </Card>
            </div>

            {/* Tabs for different views */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <div className="flex items-center justify-between">
                <TabsList>
                  <TabsTrigger value="json" className="flex items-center gap-2">
                    <FileJson className="h-4 w-4" />
                    JSON View
                  </TabsTrigger>
                  <TabsTrigger value="table" className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    Table View
                  </TabsTrigger>
                </TabsList>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportAsJSON}
                    disabled={exporting}
                  >
                    {exporting ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Download className="h-4 w-4 mr-2" />
                    )}
                    Export JSON
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportAsCSV}
                    disabled={exporting || !tableData?.rows.length}
                  >
                    {exporting ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Download className="h-4 w-4 mr-2" />
                    )}
                    Export CSV
                  </Button>
                </div>
              </div>

              <TabsContent value="json" className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">Execution Results (JSON)</CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(summary)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96">
                      <pre className="text-xs bg-muted p-4 rounded overflow-x-auto">
                        {JSON.stringify(summary, null, 2)}
                      </pre>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="table" className="space-y-4">
                {tableData && tableData.rows.length > 0 ? (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">
                        Output Data ({tableData.totalRows} records)
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-96">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              {tableData.columns.map((column) => (
                                <TableHead key={column.key} className="text-xs">
                                  {column.label}
                                </TableHead>
                              ))}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {tableData.rows.map((row, index) => (
                              <TableRow key={index}>
                                {tableData.columns.map((column) => (
                                  <TableCell key={column.key} className="text-xs max-w-xs truncate">
                                    {String(row[column.key] || '')}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="text-center py-8">
                      <FileSpreadsheet className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">No table data available</p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  );
}
